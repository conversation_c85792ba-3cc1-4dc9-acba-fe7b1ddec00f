# Exercise 4: Calculator Factory Pattern

## Problem Statement

In this exercise, create a new class `CalculatorFactory` that will take on the responsibility of providing tests with a new calculator that is already in a particular state. The test classes will ask the `CalculatorFactory` for a new Calculator as needed.

## Requirements

1. Write a new class called `CalculatorFactory` and it should have a single method `SimpleCalculator CreateWithInitialStateOf(decimal initialState)`. The `CreateWithInitialStateOf` method must create a new `SimpleCalculator` object with the value of `initialState` already entered.

2. Modify `Exercise4Tests` class to have an instance of `CalculatorFactory` as a private property.

3. Write tests for each calculator operation such that:
   - The addition test gets a calculator from `CalculatorFactory` with an initial value of 100.00 and adds 20.00.
   - The subtraction test gets a calculator from `CalculatorFactory` with an initial value of 10.00 and subtracts 5.00.
   - The multiplication test gets a calculator from `CalculatorFactory` with an initial value of 25 and multiplies it by 4.00.
   - The division test gets a calculator from `CalculatorFactory` with an initial value of 33 and divides it by 11.00

4. Make sure all tests pass.

## Step-by-Step Instructions

### Step 1: Implement the CalculatorFactory Class
1. Open `Calculators/CalculatorFactory.cs`
2. Implement the `CreateWithInitialStateOf(decimal initialState)` method
3. The method should:
   - Create a new `SimpleCalculator` instance
   - Set the initial state using the calculator's `Enter()` method
   - Return the configured calculator

### Step 2: Update the Test Class
1. Open `Exercise4Tests/Exercise4Tests.cs`
2. Add a private `CalculatorFactory` field
3. Initialize the factory in the `Setup()` method

### Step 3: Implement the Test Methods
1. **Addition Test**:
   - Get a calculator with initial state 100.00
   - Add 20.00
   - Assert result equals 120.00

2. **Subtraction Test**:
   - Get a calculator with initial state 10.00
   - Subtract 5.00
   - Assert result equals 5.00

3. **Multiplication Test**:
   - Get a calculator with initial state 25
   - Multiply by 4.00
   - Assert result equals 100.00

4. **Division Test**:
   - Get a calculator with initial state 33
   - Divide by 11.00
   - Assert result equals 3.00

### Step 4: Run and Verify Tests
```bash
dotnet build
dotnet test
```

All tests should pass when correctly implemented.
