namespace Calculators
{
    public class CalculatorFactory
    {
        public SimpleCalculator CreateWithInitialStateOf(decimal initialState)
        {
            // TODO: Implement this method
            // Create a new SimpleCalculator instance
            // Set the initial state using the calculator's Enter() method
            // Return the configured calculator
            throw new NotImplementedException();
        }
    }
}
