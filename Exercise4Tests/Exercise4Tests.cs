using Calculators;
using NUnit.Framework;

namespace Exercise4Tests
{
    public class Exercise4Tests
    {
        // TODO: Add a private CalculatorFactory field

        [SetUp]
        public void Setup()
        {
            // TODO: Initialize the CalculatorFactory
        }

        [Test]
        public void AdditionTest()
        {
            // TODO: Get a calculator from CalculatorFactory with initial value of 100.00
            // TODO: Add 20.00
            // TODO: Assert result equals 120.00
            Assert.Fail("Test not implemented");
        }

        [Test]
        public void SubtractionTest()
        {
            // TODO: Get a calculator from CalculatorFactory with initial value of 10.00
            // TODO: Subtract 5.00
            // TODO: Assert result equals 5.00
            Assert.Fail("Test not implemented");
        }

        [Test]
        public void MultiplicationTest()
        {
            // TODO: Get a calculator from CalculatorFactory with initial value of 25
            // TODO: Multiply by 4.00
            // TODO: Assert result equals 100.00
            Assert.Fail("Test not implemented");
        }

        [Test]
        public void DivisionTest()
        {
            // TODO: Get a calculator from CalculatorFactory with initial value of 33
            // TODO: Divide by 11.00
            // TODO: Assert result equals 3.00
            Assert.Fail("Test not implemented");
        }
    }
}
