using Calculators;
using Calculators.ContentSafety;
using NUnit.Framework;

namespace Exercise4Tests
{
    public class SafetyIntegrationTests
    {
        private CalculatorFactory _calculatorFactory;
        private IContentSafetyService _safetyService;
        private HttpClient _httpClient;

        [SetUp]
        public void Setup()
        {
            _calculatorFactory = new CalculatorFactory();
            _httpClient = new HttpClient();
            _safetyService = new NullContentSafetyService();
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient?.Dispose();
        }

        [Test]
        public async Task CalculatorFactory_WithSafetyService_ShouldCreateSafeCalculator()
        {
            var calculator = new SimpleCalculator(_safetyService);
            calculator.Enter(50m);
            
            var result = await calculator.Plus(25m).EqualsAsync();
            
            Assert.That(result, Is.EqualTo(75m));
            
            var safetyLog = calculator.GetSafetyLog();
            Assert.That(safetyLog.Count, Is.EqualTo(1));
            Assert.That(safetyLog[0].IsSafe, Is.True);
        }

        [Test]
        public async Task CalculatorFactory_MultipleCalculators_ShouldMaintainSeparateStates()
        {
            var calculator1 = _calculatorFactory.CreateWithInitialStateOf(10m);
            var calculator2 = new SimpleCalculator(_safetyService);
            calculator2.Enter(20m);
            
            var result1 = calculator1.Plus(5m).Equals();
            var result2 = await calculator2.Plus(10m).EqualsAsync();
            
            Assert.That(result1, Is.EqualTo(15m));
            Assert.That(result2, Is.EqualTo(30m));
            
            var safetyLog = calculator2.GetSafetyLog();
            Assert.That(safetyLog.Count, Is.EqualTo(1));
        }

        [Test]
        public async Task ContentSafetyService_DirectValidation_ShouldReturnSafe()
        {
            var safeContent = "The calculation result is 42";
            
            var result = await _safetyService.ValidateContentAsync(safeContent);
            
            Assert.That(result.IsSafe, Is.True);
            Assert.That(result.Content, Is.EqualTo(safeContent));
        }
    }
}
