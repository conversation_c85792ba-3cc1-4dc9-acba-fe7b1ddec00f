using Calculators;
using Calculators.ContentSafety;
using NUnit.Framework;

namespace Exercise4Tests
{
    public class SafetyIntegrationTests
    {
        // TODO: Add a private CalculatorFactory field
        private IContentSafetyService _safetyService;
        private HttpClient _httpClient;

        [SetUp]
        public void Setup()
        {
            // TODO: Initialize the CalculatorFactory
            _httpClient = new HttpClient();
            _safetyService = new NullContentSafetyService();
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient?.Dispose();
        }

        [Test]
        public async Task CalculatorFactory_WithSafetyService_ShouldCreateSafeCalculator()
        {
            // TODO: This test demonstrates integration with safety services
            // Create a calculator with safety service directly (not using factory for this test)
            var calculator = new SimpleCalculator(_safetyService);
            calculator.Enter(50m);

            var result = await calculator.Plus(25m).EqualsAsync();

            Assert.That(result, Is.EqualTo(75m));

            var safetyLog = calculator.GetSafetyLog();
            Assert.That(safetyLog.Count, Is.EqualTo(1));
            Assert.That(safetyLog[0].IsSafe, Is.True);
        }

        [Test]
        public async Task CalculatorFactory_MultipleCalculators_ShouldMaintainSeparateStates()
        {
            // TODO: Get a calculator from CalculatorFactory with initial value of 10
            // TODO: Create a second calculator with safety service and initial value of 20
            // TODO: Perform operations on both calculators
            // TODO: Assert both calculators maintain separate states
            Assert.Fail("Test not implemented");
        }

        [Test]
        public async Task ContentSafetyService_DirectValidation_ShouldReturnSafe()
        {
            var safeContent = "The calculation result is 42";

            var result = await _safetyService.ValidateContentAsync(safeContent);

            Assert.That(result.IsSafe, Is.True);
            Assert.That(result.Content, Is.EqualTo(safeContent));
        }
    }
}